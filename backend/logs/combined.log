{"environment":"test","level":"info","message":"Pebl backend server running on port 3000","port":3000,"service":"pebl-backend","timestamp":"2025-07-03T18:33:27.410Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /health","service":"pebl-backend","timestamp":"2025-07-03T18:33:27.419Z"}
{"body":{"deviceToken":"test-device-token","email":"<EMAIL>"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/auth/register","service":"pebl-backend","timestamp":"2025-07-03T18:33:27.430Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-03T18:33:32.436Z"}
{"level":"info","message":"WhatsApp webhook verified successfully","service":"pebl-backend","timestamp":"2025-07-03T18:33:32.437Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-03T18:33:32.444Z"}
{"level":"warn","message":"WhatsApp webhook verification failed","mode":"subscribe","service":"pebl-backend","timestamp":"2025-07-03T18:33:32.445Z","token":"wrong-token"}
{"body":{"entry":[{"changes":[{"field":"messages","value":{"messages":[{"from":"**********","id":"test-message-id","text":{"body":"Test message for categorization"},"timestamp":"**********","type":"text"}],"messaging_product":"whatsapp","metadata":{"display_phone_number":"**********","phone_number_id":"test-phone-id"}}}],"id":"test-entry-id"}],"object":"whatsapp_business_account"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-03T18:33:32.449Z"}
{"level":"warn","message":"Invalid webhook signature","service":"pebl-backend","timestamp":"2025-07-03T18:33:32.450Z"}
{"body":{"email":"<EMAIL>"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/auth/register","service":"pebl-backend","timestamp":"2025-07-03T18:33:32.458Z"}
{"level":"error","message":"Error in auth/register: Operation `users.findOne()` buffering timed out after 10000ms","service":"pebl-backend","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:594:17)\n    at processTimers (node:internal/timers:529:7)","timestamp":"2025-07-03T18:33:37.434Z"}
{"body":{"email":"<EMAIL>"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/auth/register","service":"pebl-backend","timestamp":"2025-07-03T18:33:37.458Z"}
{"level":"error","message":"Error in auth/register: Operation `users.findOne()` buffering timed out after 10000ms","service":"pebl-backend","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:594:17)\n    at processTimers (node:internal/timers:529:7)","timestamp":"2025-07-03T18:33:42.468Z"}
{"body":{"email":"<EMAIL>"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/auth/register","service":"pebl-backend","timestamp":"2025-07-03T18:33:42.473Z"}
{"level":"error","message":"Error in auth/register: Operation `users.findOne()` buffering timed out after 10000ms","service":"pebl-backend","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:594:17)\n    at processTimers (node:internal/timers:529:7)","timestamp":"2025-07-03T18:33:47.460Z"}
{"body":{"email":"<EMAIL>"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/auth/register","service":"pebl-backend","timestamp":"2025-07-03T18:33:47.475Z"}
{"level":"error","message":"Error in auth/register: Operation `users.findOne()` buffering timed out after 10000ms","service":"pebl-backend","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:594:17)\n    at processTimers (node:internal/timers:529:7)","timestamp":"2025-07-03T18:33:52.514Z"}
{"level":"error","message":"Error in auth/register: Operation `users.findOne()` buffering timed out after 10000ms","service":"pebl-backend","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:594:17)\n    at processTimers (node:internal/timers:529:7)","timestamp":"2025-07-03T18:33:57.478Z"}
{"environment":"test","level":"info","message":"Pebl backend server running on port 3000","port":3000,"service":"pebl-backend","timestamp":"2025-07-03T18:59:28.619Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /health","service":"pebl-backend","timestamp":"2025-07-03T18:59:28.630Z"}
{"body":{"deviceToken":"test-device-token","email":"<EMAIL>"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/auth/register","service":"pebl-backend","timestamp":"2025-07-03T18:59:28.644Z"}
{"level":"error","message":"Error in auth/register: User is not a constructor","service":"pebl-backend","stack":"TypeError: User is not a constructor\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/auth.js:23:14\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-03T18:59:28.646Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-03T18:59:28.650Z"}
{"level":"info","message":"WhatsApp webhook verified successfully","service":"pebl-backend","timestamp":"2025-07-03T18:59:28.650Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-03T18:59:28.652Z"}
{"level":"warn","message":"WhatsApp webhook verification failed","mode":"subscribe","service":"pebl-backend","timestamp":"2025-07-03T18:59:28.652Z","token":"wrong-token"}
{"body":{"entry":[{"changes":[{"field":"messages","value":{"messages":[{"from":"**********","id":"test-message-id","text":{"body":"Test message for categorization"},"timestamp":"**********","type":"text"}],"messaging_product":"whatsapp","metadata":{"display_phone_number":"**********","phone_number_id":"test-phone-id"}}}],"id":"test-entry-id"}],"object":"whatsapp_business_account"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-03T18:59:28.654Z"}
{"level":"warn","message":"Invalid webhook signature","service":"pebl-backend","timestamp":"2025-07-03T18:59:28.654Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/messages/user/test-user-id/pending-sync","service":"pebl-backend","timestamp":"2025-07-03T18:59:28.658Z"}
{"level":"error","message":"Error getting pending sync messages: Message.findPendingSync is not a function","service":"pebl-backend","stack":"TypeError: Message.findPendingSync is not a function\n    at MessageService.findPendingSync [as getPendingSyncMessages] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/services/MessageService.js:144:38)\n    at getPendingSyncMessages (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/messages.js:50:43)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:284:15\n    at param (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:365:14)\n    at param (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:376:14)\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:421:3)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/server.js:45:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at jsonParser (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/body-parser/lib/types/json.js:113:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express-rate-limit/dist/index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express-rate-limit/dist/index.cjs:691:5","timestamp":"2025-07-03T18:59:28.660Z"}
{"level":"error","message":"Error getting pending sync messages: Message.findPendingSync is not a function","service":"pebl-backend","stack":"TypeError: Message.findPendingSync is not a function\n    at MessageService.findPendingSync [as getPendingSyncMessages] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/services/MessageService.js:144:38)\n    at getPendingSyncMessages (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/messages.js:50:43)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:284:15\n    at param (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:365:14)\n    at param (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:376:14)\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:421:3)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/server.js:45:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at jsonParser (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/body-parser/lib/types/json.js:113:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express-rate-limit/dist/index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express-rate-limit/dist/index.cjs:691:5","timestamp":"2025-07-03T18:59:28.661Z"}
{"body":{"messageIds":["test-message-id-1","test-message-id-2"]},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/messages/mark-synced","service":"pebl-backend","timestamp":"2025-07-03T18:59:28.663Z"}
{"count":2,"level":"info","message":"Messages marked as synced","messageIds":2,"service":"pebl-backend","timestamp":"2025-07-03T18:59:28.663Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/users/test-user-id","service":"pebl-backend","timestamp":"2025-07-03T18:59:28.665Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"PATCH /api/users/test-user-id/preferences","service":"pebl-backend","timestamp":"2025-07-03T18:59:28.668Z"}
{"level":"error","message":"Error updating user preferences: user.save is not a function","service":"pebl-backend","stack":"TypeError: user.save is not a function\n    at save (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/users.js:62:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-03T18:59:28.669Z"}
{"environment":"test","level":"info","message":"Pebl backend server running on port 3000","port":3000,"service":"pebl-backend","timestamp":"2025-07-03T19:16:26.362Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /health","service":"pebl-backend","timestamp":"2025-07-03T19:16:26.371Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-03T19:16:26.381Z"}
{"level":"info","message":"WhatsApp webhook verified successfully","service":"pebl-backend","timestamp":"2025-07-03T19:16:26.381Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-03T19:16:26.383Z"}
{"level":"warn","message":"WhatsApp webhook verification failed","mode":"subscribe","service":"pebl-backend","timestamp":"2025-07-03T19:16:26.384Z","token":"wrong-token"}
{"body":{"entry":[{"changes":[{"field":"messages","value":{"messages":[{"from":"**********","id":"test-message-id","text":{"body":"Test message for categorization"},"timestamp":"**********","type":"text"}],"messaging_product":"whatsapp","metadata":{"display_phone_number":"**********","phone_number_id":"test-phone-id"}}}],"id":"test-entry-id"}],"object":"whatsapp_business_account"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-03T19:16:26.391Z"}
{"level":"warn","message":"Invalid webhook signature","service":"pebl-backend","timestamp":"2025-07-03T19:16:26.391Z"}
{"environment":"test","level":"info","message":"Pebl backend server running on port 3000","port":3000,"service":"pebl-backend","timestamp":"2025-07-03T19:17:41.058Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /health","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.068Z"}
{"body":{"deviceToken":"test-device-token","email":"<EMAIL>"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/auth/register","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.081Z"}
{"email":"<EMAIL>","level":"info","message":"New user created","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.082Z","userId":"test-user-id"}
{"level":"error","message":"Error in auth/register: user.addDeviceToken is not a function","service":"pebl-backend","stack":"TypeError: user.addDeviceToken is not a function\n    at addDeviceToken (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/auth.js:32:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-03T19:17:41.084Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.088Z"}
{"level":"info","message":"WhatsApp webhook verified successfully","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.088Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.090Z"}
{"level":"warn","message":"WhatsApp webhook verification failed","mode":"subscribe","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.090Z","token":"wrong-token"}
{"body":{"entry":[{"changes":[{"field":"messages","value":{"messages":[{"from":"**********","id":"test-message-id","text":{"body":"Test message for categorization"},"timestamp":"**********","type":"text"}],"messaging_product":"whatsapp","metadata":{"display_phone_number":"**********","phone_number_id":"test-phone-id"}}}],"id":"test-entry-id"}],"object":"whatsapp_business_account"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.092Z"}
{"level":"warn","message":"Invalid webhook signature","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.092Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/messages/user/test-user-id/pending-sync","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.096Z"}
{"level":"error","message":"Error getting pending sync messages: Cannot read properties of undefined (reading 'length')","service":"pebl-backend","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at length (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/messages.js:55:23)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-03T19:17:41.097Z"}
{"body":{"messageIds":["test-message-id-1","test-message-id-2"]},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/messages/mark-synced","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.098Z"}
{"count":2,"level":"info","message":"Messages marked as synced","messageIds":2,"service":"pebl-backend","timestamp":"2025-07-03T19:17:41.099Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/users/test-user-id","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.101Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"PATCH /api/users/test-user-id/preferences","service":"pebl-backend","timestamp":"2025-07-03T19:17:41.104Z"}
{"level":"error","message":"Error updating user preferences: user.save is not a function","service":"pebl-backend","stack":"TypeError: user.save is not a function\n    at save (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/users.js:62:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-03T19:17:41.105Z"}
{"environment":"test","level":"info","message":"Pebl backend server running on port 3000","port":3000,"service":"pebl-backend","timestamp":"2025-07-08T15:25:49.183Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /health","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.197Z"}
{"body":{"deviceToken":"test-device-token","email":"<EMAIL>"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/auth/register","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.209Z"}
{"email":"<EMAIL>","level":"info","message":"New user created","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.210Z","userId":"test-user-id"}
{"level":"info","message":"Device token added","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.211Z","userId":"test-user-id"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.216Z"}
{"level":"info","message":"WhatsApp webhook verified successfully","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.216Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.220Z"}
{"level":"warn","message":"WhatsApp webhook verification failed","mode":"subscribe","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.220Z","token":"wrong-token"}
{"body":{"entry":[{"changes":[{"field":"messages","value":{"messages":[{"from":"**********","id":"test-message-id","text":{"body":"Test message for categorization"},"timestamp":"**********","type":"text"}],"messaging_product":"whatsapp","metadata":{"display_phone_number":"**********","phone_number_id":"test-phone-id"}}}],"id":"test-entry-id"}],"object":"whatsapp_business_account"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/whatsapp/webhook","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.223Z"}
{"level":"warn","message":"Invalid webhook signature","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.223Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/messages/user/test-user-id/pending-sync","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.227Z"}
{"body":{"messageIds":["test-message-id-1","test-message-id-2"]},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /api/messages/mark-synced","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.229Z"}
{"count":2,"level":"info","message":"Messages marked as synced","messageIds":2,"service":"pebl-backend","timestamp":"2025-07-08T15:25:49.229Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/users/test-user-id","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.235Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"PATCH /api/users/test-user-id/preferences","service":"pebl-backend","timestamp":"2025-07-08T15:25:49.241Z"}
{"level":"error","message":"Error updating user preferences: user.save is not a function","service":"pebl-backend","stack":"TypeError: user.save is not a function\n    at save (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/users.js:62:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-08T15:25:49.243Z"}
