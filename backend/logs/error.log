{"level":"error","message":"Error in auth/register: Operation `users.findOne()` buffering timed out after 10000ms","service":"pebl-backend","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:594:17)\n    at processTimers (node:internal/timers:529:7)","timestamp":"2025-07-03T18:33:37.434Z"}
{"level":"error","message":"Error in auth/register: Operation `users.findOne()` buffering timed out after 10000ms","service":"pebl-backend","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:594:17)\n    at processTimers (node:internal/timers:529:7)","timestamp":"2025-07-03T18:33:42.468Z"}
{"level":"error","message":"Error in auth/register: Operation `users.findOne()` buffering timed out after 10000ms","service":"pebl-backend","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:594:17)\n    at processTimers (node:internal/timers:529:7)","timestamp":"2025-07-03T18:33:47.460Z"}
{"level":"error","message":"Error in auth/register: Operation `users.findOne()` buffering timed out after 10000ms","service":"pebl-backend","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:594:17)\n    at processTimers (node:internal/timers:529:7)","timestamp":"2025-07-03T18:33:52.514Z"}
{"level":"error","message":"Error in auth/register: Operation `users.findOne()` buffering timed out after 10000ms","service":"pebl-backend","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:594:17)\n    at processTimers (node:internal/timers:529:7)","timestamp":"2025-07-03T18:33:57.478Z"}
{"level":"error","message":"Error in auth/register: User is not a constructor","service":"pebl-backend","stack":"TypeError: User is not a constructor\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/auth.js:23:14\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-03T18:59:28.646Z"}
{"level":"error","message":"Error getting pending sync messages: Message.findPendingSync is not a function","service":"pebl-backend","stack":"TypeError: Message.findPendingSync is not a function\n    at MessageService.findPendingSync [as getPendingSyncMessages] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/services/MessageService.js:144:38)\n    at getPendingSyncMessages (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/messages.js:50:43)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:284:15\n    at param (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:365:14)\n    at param (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:376:14)\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:421:3)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/server.js:45:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at jsonParser (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/body-parser/lib/types/json.js:113:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express-rate-limit/dist/index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express-rate-limit/dist/index.cjs:691:5","timestamp":"2025-07-03T18:59:28.660Z"}
{"level":"error","message":"Error getting pending sync messages: Message.findPendingSync is not a function","service":"pebl-backend","stack":"TypeError: Message.findPendingSync is not a function\n    at MessageService.findPendingSync [as getPendingSyncMessages] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/services/MessageService.js:144:38)\n    at getPendingSyncMessages (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/messages.js:50:43)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:284:15\n    at param (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:365:14)\n    at param (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:376:14)\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:421:3)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/server.js:45:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at jsonParser (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/body-parser/lib/types/json.js:113:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express-rate-limit/dist/index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at /Users/<USER>/Documents/Xcode apps/Pebl/backend/node_modules/express-rate-limit/dist/index.cjs:691:5","timestamp":"2025-07-03T18:59:28.661Z"}
{"level":"error","message":"Error updating user preferences: user.save is not a function","service":"pebl-backend","stack":"TypeError: user.save is not a function\n    at save (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/users.js:62:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-03T18:59:28.669Z"}
{"level":"error","message":"Error in auth/register: user.addDeviceToken is not a function","service":"pebl-backend","stack":"TypeError: user.addDeviceToken is not a function\n    at addDeviceToken (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/auth.js:32:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-03T19:17:41.084Z"}
{"level":"error","message":"Error getting pending sync messages: Cannot read properties of undefined (reading 'length')","service":"pebl-backend","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at length (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/messages.js:55:23)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-03T19:17:41.097Z"}
{"level":"error","message":"Error updating user preferences: user.save is not a function","service":"pebl-backend","stack":"TypeError: user.save is not a function\n    at save (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/users.js:62:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-03T19:17:41.105Z"}
{"level":"error","message":"Error updating user preferences: user.save is not a function","service":"pebl-backend","stack":"TypeError: user.save is not a function\n    at save (/Users/<USER>/Documents/Xcode apps/Pebl/backend/src/routes/users.js:62:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-08T15:25:49.243Z"}
