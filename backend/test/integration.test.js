const request = require('supertest');

// Set test environment before requiring the app
process.env.NODE_ENV = 'test';
process.env.MONGODB_URI = 'mongodb://localhost:27017/pebl_test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN = 'test-verify-token';

// Mock the database connection for tests
jest.mock('../src/config/database', () => jest.fn().mockResolvedValue(true));

const app = require('../src/server');

describe('Backend Integration Tests', () => {
  
  describe('Health Check', () => {
    it('should return 200 for health check', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);
      
      expect(response.body.status).toBe('healthy');
    });
  });

  describe('Authentication', () => {
    it('should register a new user', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          deviceToken: 'test-device-token'
        })
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.user.email).toBe('<EMAIL>');
      expect(response.body.token).toBeDefined();
    });
  });

  describe('WhatsApp Webhook', () => {
    it('should verify webhook with correct token', async () => {
      const response = await request(app)
        .get('/api/whatsapp/webhook')
        .query({
          'hub.mode': 'subscribe',
          'hub.challenge': 'test-challenge',
          'hub.verify_token': process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN || 'test-verify-token'
        })
        .expect(200);
      
      expect(response.text).toBe('test-challenge');
    });

    it('should reject webhook with incorrect token', async () => {
      await request(app)
        .get('/api/whatsapp/webhook')
        .query({
          'hub.mode': 'subscribe',
          'hub.challenge': 'test-challenge',
          'hub.verify_token': 'wrong-token'
        })
        .expect(403);
    });
  });

  describe('Message Processing', () => {
    it('should process a simple text message', async () => {
      const mockWebhookPayload = {
        object: 'whatsapp_business_account',
        entry: [{
          id: 'test-entry-id',
          changes: [{
            value: {
              messaging_product: 'whatsapp',
              metadata: {
                display_phone_number: '**********',
                phone_number_id: 'test-phone-id'
              },
              messages: [{
                from: '**********',
                id: 'test-message-id',
                timestamp: '**********',
                text: {
                  body: 'Test message for categorization'
                },
                type: 'text'
              }]
            },
            field: 'messages'
          }]
        }]
      };

      // Note: This test would need a registered user with WhatsApp number
      // For now, we'll just test that the endpoint accepts the payload
      const response = await request(app)
        .post('/api/whatsapp/webhook')
        .send(mockWebhookPayload);
      
      // Should return 200 even if user not found (webhook should always acknowledge)
      expect(response.status).toBe(200);
    });
  });

  describe('Categorization Service', () => {
    it('should categorize a hashtag message correctly', () => {
      const CategorizationService = require('../src/services/CategorizationService');
      
      const result = CategorizationService.parseHashtagCategory('#shopping Buy groceries');
      
      expect(result.hasHashtag).toBe(true);
      expect(result.categoryName).toBe('Shopping');
      expect(result.messageContent).toBe('Buy groceries');
    });

    it('should categorize a subcategory hashtag message correctly', () => {
      const CategorizationService = require('../src/services/CategorizationService');
      
      const result = CategorizationService.parseHashtagCategory('#work/meetings Schedule team sync');
      
      expect(result.hasHashtag).toBe(true);
      expect(result.categoryName).toBe('Work');
      expect(result.subcategoryName).toBe('Meetings');
      expect(result.messageContent).toBe('Schedule team sync');
    });
  });

  describe('Message Sync', () => {
    let userId;
    
    beforeEach(async () => {
      // Create a test user
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: `test-${Date.now()}@example.com`
        });
      
      userId = response.body.user.id;
    });

    it('should return empty pending sync messages for new user', async () => {
      const response = await request(app)
        .get(`/api/messages/user/${userId}/pending-sync`)
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.messages).toEqual([]);
      expect(response.body.count).toBe(0);
    });

    it('should mark messages as synced', async () => {
      const response = await request(app)
        .post('/api/messages/mark-synced')
        .send({
          messageIds: ['test-message-id-1', 'test-message-id-2']
        })
        .expect(200);
      
      expect(response.body.success).toBe(true);
    });
  });

  describe('User Management', () => {
    let userId;
    
    beforeEach(async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: `test-${Date.now()}@example.com`
        });
      
      userId = response.body.user.id;
    });

    it('should get user profile', async () => {
      const response = await request(app)
        .get(`/api/users/${userId}`)
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.user.id).toBe(userId);
    });

    it('should update user preferences', async () => {
      const preferences = {
        messageExpirationDays: 30,
        autoDeleteExpiredMessages: true,
        enableWhatsappSync: true,
        notificationSettings: {
          pushNotifications: true,
          whatsappMessages: true
        }
      };

      const response = await request(app)
        .patch(`/api/users/${userId}/preferences`)
        .send({ preferences })
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.preferences.messageExpirationDays).toBe(30);
    });
  });
});

// Test helper to clean up test data
afterAll(async () => {
  // Clean up test data if needed
  // This would depend on your database setup
});
