# Test Environment Configuration
NODE_ENV=test
PORT=3001

# MongoDB Test Database
MONGODB_URI=mongodb://localhost:27017/pebl_test

# JWT Secret for testing
JWT_SECRET=test-jwt-secret-key-for-testing-only

# WhatsApp Business API Test Configuration
WHATSAPP_ACCESS_TOKEN=test-access-token
WHATSAPP_PHONE_NUMBER_ID=test-phone-number-id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=test-verify-token

# OpenAI API Test Configuration
OPENAI_API_KEY=test-openai-api-key

# Push Notification Test Configuration
APNS_KEY_ID=test-key-id
APNS_TEAM_ID=test-team-id
APNS_BUNDLE_ID=com.example.pebl.test
APNS_PRODUCTION=false

# Test-specific settings
ENABLE_RATE_LIMITING=false
ENABLE_WEBHOOK_VERIFICATION=false
