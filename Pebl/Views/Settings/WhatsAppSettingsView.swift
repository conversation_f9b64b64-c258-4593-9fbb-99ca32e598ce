//
//  WhatsAppSettingsView.swift
//  Pebl
//
//  Created by AI Assistant on 7/3/25.
//

import SwiftUI

struct WhatsAppSettingsView: View {
    @StateObject private var whatsappService = WhatsAppIntegrationService.shared
    @StateObject private var syncService = SyncService.shared
    @EnvironmentObject var categoryManager: CategoryManager
    @EnvironmentObject var userManager: UserManager
    
    @State private var phoneNumber = ""
    @State private var verificationCode = ""
    @State private var showingPhoneInput = false
    @State private var showingVerificationInput = false
    @State private var showingDisconnectAlert = false
    @State private var showingAuthenticationPrompt = false
    
    var body: some View {
        NavigationView {
            List {
                // Status Section
                Section {
                    HStack {
                        Image(systemName: whatsappService.isVerified ? "checkmark.circle.fill" : "circle")
                            .foregroundColor(whatsappService.isVerified ? .green : .gray)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("WhatsApp Integration")
                                .font(.headline)
                            
                            Text(statusText)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if whatsappService.isVerified {
                            Text("Connected")
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.green.opacity(0.2))
                                .foregroundColor(.green)
                                .cornerRadius(8)
                        }
                    }
                    .padding(.vertical, 4)
                } header: {
                    Text("Status")
                }
                
                // Connection Section
                Section {
                    if !whatsappService.isLinked {
                        // Connect Button
                        Button(action: {
                            // WhatsApp connection requires authentication
                            if userManager.isAuthenticated {
                                showingPhoneInput = true
                            } else {
                                // Prompt user to authenticate first
                                showingAuthenticationPrompt = true
                            }
                        }) {
                            HStack {
                                Image(systemName: "link")
                                Text("Connect WhatsApp")
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .disabled(whatsappService.isLinking)
                    } else if !whatsappService.isVerified {
                        // Verify Button
                        Button(action: {
                            showingVerificationInput = true
                        }) {
                            HStack {
                                Image(systemName: "key")
                                Text("Verify Number")
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .disabled(whatsappService.isVerifying)
                        
                        // Resend Code Button
                        Button(action: {
                            if let number = whatsappService.whatsappNumber {
                                whatsappService.linkWhatsAppNumber(number)
                            }
                        }) {
                            HStack {
                                Image(systemName: "arrow.clockwise")
                                Text("Resend Verification Code")
                            }
                        }
                        .disabled(whatsappService.isLinking)
                    } else {
                        // Connected - Show number and disconnect option
                        if let number = whatsappService.whatsappNumber {
                            HStack {
                                Image(systemName: "phone")
                                Text(number)
                                Spacer()
                            }
                        }
                        
                        Button(action: {
                            showingDisconnectAlert = true
                        }) {
                            HStack {
                                Image(systemName: "link.badge.minus")
                                Text("Disconnect WhatsApp")
                            }
                            .foregroundColor(.red)
                        }
                    }
                } header: {
                    Text("Connection")
                }
                
                // Sync Section
                if whatsappService.isVerified {
                    Section {
                        HStack {
                            Image(systemName: "arrow.triangle.2.circlepath")
                            Text("Last Sync")
                            Spacer()
                            Text(lastSyncText)
                                .foregroundColor(.secondary)
                        }
                        
                        Button(action: {
                            // Sync requires authentication - prompt user to sign in first
                            if userManager.isAuthenticated, let userId = userManager.currentUserId {
                                syncService.syncPendingMessages(for: userId, categoryManager: categoryManager)
                            } else {
                                // Prompt user to authenticate for sync functionality
                                userManager.authenticateUser(email: "")
                            }
                        }) {
                            HStack {
                                if syncService.isSyncing {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "arrow.clockwise")
                                }
                                Text("Sync Now")
                            }
                        }
                        .disabled(syncService.isSyncing)
                        
                        if let error = syncService.syncError {
                            HStack {
                                Image(systemName: "exclamationmark.triangle")
                                    .foregroundColor(.orange)
                                Text(error)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    } header: {
                        Text("Sync")
                    }
                }
                
                // Instructions Section
                Section {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("How it works:")
                            .font(.headline)

                        Text("1. Sign in with your email (for secure sync)")
                        Text("2. Connect your WhatsApp number")
                        Text("3. Verify with the code sent to WhatsApp")
                        Text("4. Send messages to your WhatsApp Business number")
                        Text("5. Messages will automatically appear in Pebl")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                } header: {
                    Text("Instructions")
                }
            }
            .navigationTitle("WhatsApp")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingPhoneInput) {
            PhoneNumberInputView(
                phoneNumber: $phoneNumber,
                isLoading: whatsappService.isLinking,
                error: whatsappService.linkingError
            ) {
                whatsappService.linkWhatsAppNumber(phoneNumber)
                showingPhoneInput = false
            }
        }
        .sheet(isPresented: $showingVerificationInput) {
            VerificationCodeInputView(
                verificationCode: $verificationCode,
                isLoading: whatsappService.isVerifying,
                error: whatsappService.verificationError
            ) {
                whatsappService.verifyWhatsAppNumber(with: verificationCode)
                showingVerificationInput = false
            }
        }
        .alert("Disconnect WhatsApp", isPresented: $showingDisconnectAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Disconnect", role: .destructive) {
                whatsappService.disconnectWhatsApp()
            }
        } message: {
            Text("Are you sure you want to disconnect WhatsApp? You'll need to verify your number again to reconnect.")
        }
        .sheet(isPresented: $showingAuthenticationPrompt) {
            AuthenticationPromptView(userManager: userManager) {
                showingAuthenticationPrompt = false
                if userManager.isAuthenticated {
                    showingPhoneInput = true
                }
            }
        }
        .onAppear {
            // WhatsApp settings can be viewed without authentication
            // Actual connection requires authentication
            if userManager.isAuthenticated, let userId = userManager.currentUserId {
                whatsappService.initialize(for: userId)
            }
        }
    }

    // MARK: - Computed Properties

    private var statusText: String {
        if !userManager.isAuthenticated {
            return "Sign in required to connect WhatsApp"
        } else if whatsappService.isVerified {
            return "Messages from WhatsApp will sync automatically"
        } else if whatsappService.isLinked {
            return "Verification required to complete setup"
        } else {
            return "Connect your WhatsApp to sync messages"
        }
    }

    private var lastSyncText: String {
        if let lastSync = syncService.lastSyncDate {
            let formatter = RelativeDateTimeFormatter()
            return formatter.localizedString(for: lastSync, relativeTo: Date())
        } else {
            return "Never"
        }
    }
}

// MARK: - Phone Number Input View

struct PhoneNumberInputView: View {
    @Binding var phoneNumber: String
    let isLoading: Bool
    let error: String?
    let onSubmit: () -> Void
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Enter your WhatsApp number")
                        .font(.headline)
                    
                    Text("Include your country code (e.g., +1 for US)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    TextField("+1234567890", text: $phoneNumber)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.phonePad)
                }
                
                if let error = error {
                    Text(error)
                        .foregroundColor(.red)
                        .font(.caption)
                }
                
                Button(action: onSubmit) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        Text("Send Verification Code")
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
                .disabled(phoneNumber.isEmpty || isLoading)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Connect WhatsApp")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Verification Code Input View

struct VerificationCodeInputView: View {
    @Binding var verificationCode: String
    let isLoading: Bool
    let error: String?
    let onSubmit: () -> Void
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Enter verification code")
                        .font(.headline)
                    
                    Text("Check your WhatsApp for the verification code")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    TextField("123456", text: $verificationCode)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.numberPad)
                        .textContentType(.oneTimeCode)
                }
                
                if let error = error {
                    Text(error)
                        .foregroundColor(.red)
                        .font(.caption)
                }
                
                Button(action: onSubmit) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        Text("Verify")
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
                .disabled(verificationCode.isEmpty || isLoading)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Verify Number")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Authentication Prompt View

struct AuthenticationPromptView: View {
    @ObservedObject var userManager: UserManager
    let onComplete: () -> Void

    @State private var email = ""
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(spacing: 16) {
                    Image(systemName: "person.circle")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)

                    Text("Sign In Required")
                        .font(.title2)
                        .fontWeight(.semibold)

                    Text("WhatsApp integration requires an account to sync your messages securely.")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()

                VStack(spacing: 16) {
                    TextField("Enter your email", text: $email)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)

                    if let error = userManager.authError {
                        Text(error)
                            .foregroundColor(.red)
                            .font(.caption)
                    }

                    Button(action: {
                        userManager.authenticateUser(email: email)
                    }) {
                        HStack {
                            if userManager.isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .foregroundColor(.white)
                            }
                            Text(userManager.isLoading ? "Signing In..." : "Sign In")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(email.isEmpty || userManager.isLoading)
                }
                .padding(.horizontal)

                Spacer()
            }
            .navigationTitle("WhatsApp Setup")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
        .onChange(of: userManager.isAuthenticated) { isAuthenticated in
            if isAuthenticated {
                onComplete()
            }
        }
    }
}

#Preview {
    WhatsAppSettingsView()
        .environmentObject(CategoryManager())
}
