//
//  PeblApp.swift
//  Pebl
//
//  Created by <PERSON><PERSON> on 6/4/25.
//

import SwiftUI

@main
struct PeblApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var userManager = UserManager.shared
    @StateObject private var categoryManager = CategoryManager.shared
    @StateObject private var settingsManager = SettingsManager()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(categoryManager)
                .environmentObject(settingsManager)
                .environmentObject(userManager)
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("NavigateToCategory"))) { notification in
                    // Handle navigation to category from push notification
                    if let categoryName = notification.userInfo?["categoryName"] as? String {
                        // This would need to be implemented in ContentView
                        print("📍 Navigate to category: \(categoryName)")
                    }
                }
        }
    }
}
